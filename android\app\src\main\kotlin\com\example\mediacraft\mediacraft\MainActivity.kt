package com.example.mediacraft.mediacraft

import android.app.WallpaperManager
import android.content.ContentValues
import android.content.Intent
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.media.MediaMuxer
import android.media.MediaExtractor
import android.media.MediaFormat
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.provider.Settings
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.nio.ByteBuffer

class MainActivity: FlutterActivity() {
    private val AUDIO_CHANNEL = "mediacraft/audio"
    private val WALLPAPER_CHANNEL = "mediacraft/wallpaper"

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Audio channel for ringtone functionality and audio processing
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, AUDIO_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "setRingtone" -> {
                    val audioPath = call.argument<String>("audioPath")
                    if (audioPath != null) {
                        setRingtone(audioPath, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Audio path is required", null)
                    }
                }
                "cutAudio" -> {
                    val inputPath = call.argument<String>("inputPath")
                    val outputPath = call.argument<String>("outputPath")
                    val startTimeMs = call.argument<Long>("startTimeMs")
                    val endTimeMs = call.argument<Long>("endTimeMs")

                    if (inputPath != null && outputPath != null && startTimeMs != null && endTimeMs != null) {
                        cutAudio(inputPath, outputPath, startTimeMs, endTimeMs, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Required parameters missing", null)
                    }
                }
                "applyReverbEffect" -> {
                    val inputPath = call.argument<String>("inputPath")
                    val outputPath = call.argument<String>("outputPath")
                    val roomSize = call.argument<Double>("roomSize") ?: 0.5
                    val damping = call.argument<Double>("damping") ?: 0.5
                    val wetLevel = call.argument<Double>("wetLevel") ?: 0.3

                    if (inputPath != null && outputPath != null) {
                        applyReverbEffect(inputPath, outputPath, roomSize, damping, wetLevel, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Required parameters missing", null)
                    }
                }
                "applySpeedEffect" -> {
                    val inputPath = call.argument<String>("inputPath")
                    val outputPath = call.argument<String>("outputPath")
                    val speed = call.argument<Double>("speed") ?: 1.0

                    if (inputPath != null && outputPath != null) {
                        applySpeedEffect(inputPath, outputPath, speed, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Required parameters missing", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Wallpaper channel for wallpaper functionality
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, WALLPAPER_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "setWallpaper" -> {
                    val videoPath = call.argument<String>("videoPath")
                    val setHomeScreen = call.argument<Boolean>("setHomeScreen") ?: true
                    val setLockScreen = call.argument<Boolean>("setLockScreen") ?: true

                    if (videoPath != null) {
                        setWallpaper(videoPath, setHomeScreen, setLockScreen, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Video path is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun setRingtone(audioPath: String, result: MethodChannel.Result) {
        try {
            val audioFile = File(audioPath)
            if (!audioFile.exists()) {
                result.error("FILE_NOT_FOUND", "Audio file not found", null)
                return
            }

            // Check if we have permission to write settings
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!Settings.System.canWrite(this)) {
                    // Request permission to write settings
                    val intent = Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS)
                    intent.data = Uri.parse("package:$packageName")
                    startActivity(intent)
                    result.error("PERMISSION_DENIED", "Permission to write settings is required", null)
                    return
                }
            }

            // For Android 10+ (API 29+), use different approach
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Use MediaStore with proper scoped storage
                val values = ContentValues().apply {
                    put(MediaStore.MediaColumns.DISPLAY_NAME, audioFile.nameWithoutExtension)
                    put(MediaStore.MediaColumns.MIME_TYPE, "audio/mpeg")
                    put(MediaStore.Audio.Media.IS_RINGTONE, true)
                    put(MediaStore.Audio.Media.IS_NOTIFICATION, false)
                    put(MediaStore.Audio.Media.IS_ALARM, false)
                    put(MediaStore.Audio.Media.IS_MUSIC, false)
                }

                val uri = contentResolver.insert(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, values)

                if (uri != null) {
                    // Copy file content to the new URI
                    contentResolver.openOutputStream(uri)?.use { outputStream ->
                        audioFile.inputStream().use { inputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }

                    // Set as default ringtone
                    RingtoneManager.setActualDefaultRingtoneUri(
                        this,
                        RingtoneManager.TYPE_RINGTONE,
                        uri
                    )
                    result.success(true)
                } else {
                    result.error("FAILED", "Failed to create ringtone entry", null)
                }
            } else {
                // Legacy approach for older Android versions
                val values = ContentValues().apply {
                    put(MediaStore.MediaColumns.DATA, audioPath)
                    put(MediaStore.MediaColumns.TITLE, audioFile.nameWithoutExtension)
                    put(MediaStore.MediaColumns.MIME_TYPE, "audio/mpeg")
                    put(MediaStore.Audio.Media.IS_RINGTONE, true)
                    put(MediaStore.Audio.Media.IS_NOTIFICATION, false)
                    put(MediaStore.Audio.Media.IS_ALARM, false)
                    put(MediaStore.Audio.Media.IS_MUSIC, false)
                }

                val uri = contentResolver.insert(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, values)

                if (uri != null) {
                    // Set as default ringtone
                    RingtoneManager.setActualDefaultRingtoneUri(
                        this,
                        RingtoneManager.TYPE_RINGTONE,
                        uri
                    )
                    result.success(true)
                } else {
                    result.error("FAILED", "Failed to set ringtone", null)
                }
            }
        } catch (e: Exception) {
            result.error("ERROR", "Error setting ringtone: ${e.message}", null)
        }
    }

    private fun setWallpaper(videoPath: String, setHomeScreen: Boolean, setLockScreen: Boolean, result: MethodChannel.Result) {
        try {
            val videoFile = File(videoPath)
            if (!videoFile.exists()) {
                result.error("FILE_NOT_FOUND", "Video file not found", null)
                return
            }

            // Check if the file is actually an image (for static wallpapers)
            val isImage = videoPath.lowercase().endsWith(".jpg") ||
                         videoPath.lowercase().endsWith(".jpeg") ||
                         videoPath.lowercase().endsWith(".png") ||
                         videoPath.lowercase().endsWith(".bmp")

            if (isImage) {
                // Handle static image wallpaper
                try {
                    val wallpaperManager = WallpaperManager.getInstance(this)
                    val bitmap = BitmapFactory.decodeFile(videoPath)

                    if (bitmap != null) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            // Android 7.0+ supports separate home and lock screen wallpapers
                            if (setHomeScreen && setLockScreen) {
                                wallpaperManager.setBitmap(bitmap, null, true, WallpaperManager.FLAG_SYSTEM or WallpaperManager.FLAG_LOCK)
                            } else if (setHomeScreen) {
                                wallpaperManager.setBitmap(bitmap, null, true, WallpaperManager.FLAG_SYSTEM)
                            } else if (setLockScreen) {
                                wallpaperManager.setBitmap(bitmap, null, true, WallpaperManager.FLAG_LOCK)
                            }
                        } else {
                            // Older Android versions - set for both
                            wallpaperManager.setBitmap(bitmap)
                        }
                        result.success(true)
                        return
                    } else {
                        result.error("INVALID_IMAGE", "Could not decode image file", null)
                        return
                    }
                } catch (e: Exception) {
                    result.error("WALLPAPER_ERROR", "Failed to set image wallpaper: ${e.message}", null)
                    return
                }
            }

            // For video files, try to open with live wallpaper apps
            val intent = Intent(Intent.ACTION_SET_WALLPAPER)
            intent.setDataAndType(Uri.fromFile(videoFile), "video/*")
            intent.putExtra("mimeType", "video/*")
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)

            if (intent.resolveActivity(packageManager) != null) {
                startActivity(Intent.createChooser(intent, "Set Live Wallpaper"))
                result.success(true)
            } else {
                // Fallback: try to open with video player or file manager
                val fallbackIntent = Intent(Intent.ACTION_VIEW)
                fallbackIntent.setDataAndType(Uri.fromFile(videoFile), "video/*")
                fallbackIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                fallbackIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)

                if (fallbackIntent.resolveActivity(packageManager) != null) {
                    startActivity(Intent.createChooser(fallbackIntent, "Open with"))
                    result.success(true)
                } else {
                    result.error("NO_APP", "No app found to handle video wallpaper. Please install a live wallpaper app.", null)
                }
            }
        } catch (e: Exception) {
            result.error("ERROR", "Error setting wallpaper: ${e.message}", null)
        }
    }

    private fun cutAudio(inputPath: String, outputPath: String, startTimeMs: Long, endTimeMs: Long, result: MethodChannel.Result) {
        try {
            val inputFile = File(inputPath)
            if (!inputFile.exists()) {
                result.error("FILE_NOT_FOUND", "Input audio file not found", null)
                return
            }

            // Simple audio cutting using file copying with byte range
            // This is a basic implementation - for production, you'd want more sophisticated audio processing
            val inputStream = FileInputStream(inputFile)
            val outputStream = FileOutputStream(outputPath)

            // Calculate approximate byte positions (this is simplified)
            val fileSize = inputFile.length()
            val totalDurationMs = getAudioDuration(inputPath)

            if (totalDurationMs > 0) {
                val startRatio = startTimeMs.toDouble() / totalDurationMs
                val endRatio = endTimeMs.toDouble() / totalDurationMs

                val startByte = (fileSize * startRatio).toLong()
                val endByte = (fileSize * endRatio).toLong()

                inputStream.skip(startByte)
                val buffer = ByteArray(8192)
                var totalRead = 0L
                var bytesRead: Int

                while (totalRead < (endByte - startByte)) {
                    bytesRead = inputStream.read(buffer)
                    if (bytesRead == -1) break

                    val bytesToWrite = minOf(bytesRead, (endByte - startByte - totalRead).toInt())
                    outputStream.write(buffer, 0, bytesToWrite)
                    totalRead += bytesToWrite
                }
            } else {
                // Fallback: copy entire file
                inputStream.copyTo(outputStream)
            }

            inputStream.close()
            outputStream.close()

            result.success(true)
        } catch (e: Exception) {
            result.error("ERROR", "Error cutting audio: ${e.message}", null)
        }
    }

    private fun applyReverbEffect(inputPath: String, outputPath: String, roomSize: Double, damping: Double, wetLevel: Double, result: MethodChannel.Result) {
        try {
            // For now, copy the file as reverb requires complex audio processing
            // In a production app, you would use Android's AudioEffect API or native audio libraries
            val inputFile = File(inputPath)
            val outputFile = File(outputPath)

            inputFile.copyTo(outputFile, overwrite = true)

            // TODO: Implement actual reverb processing using AudioEffect or native libraries
            result.success(true)
        } catch (e: Exception) {
            result.error("ERROR", "Error applying reverb effect: ${e.message}", null)
        }
    }

    private fun applySpeedEffect(inputPath: String, outputPath: String, speed: Double, result: MethodChannel.Result) {
        try {
            // For now, copy the file as speed change requires complex audio processing
            // In a production app, you would use MediaMuxer/MediaExtractor or native audio libraries
            val inputFile = File(inputPath)
            val outputFile = File(outputPath)

            inputFile.copyTo(outputFile, overwrite = true)

            // TODO: Implement actual speed processing using MediaMuxer/MediaExtractor
            result.success(true)
        } catch (e: Exception) {
            result.error("ERROR", "Error applying speed effect: ${e.message}", null)
        }
    }

    private fun getAudioDuration(audioPath: String): Long {
        return try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(audioPath)
            val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            retriever.release()
            duration?.toLongOrNull() ?: 0L
        } catch (e: Exception) {
            0L
        }
    }
}
